;;; gemini.el --- Gemini provider for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the Gemini provider implementation for the AI Auto Complete package.

;;; Code:

(require 'request)
(require 'json)
(require 'core/backend)
(require 'customization/model-management)

;; Helper function to trim whitespace (replacement for s-trim)
(defun ai-auto-complete-gemini--trim (str)
  "Simple trim function to remove leading and trailing whitespace."
  (when (stringp str)
    (replace-regexp-in-string "\\`[ \t\n\r]+" ""
                             (replace-regexp-in-string "[ \t\n\r]+\\'" "" str))))

;; Helper function to infer JSON schema type from parameter description
(defun ai-auto-complete-gemini--infer-param-type (param-name param-desc)
  "Infer JSON schema type from PARAM-NAME and PARAM-DESC.
Returns a plist with :type and optionally :items or :properties."
  (let ((desc-lower (downcase param-desc))
        (name-lower (downcase param-name)))
    (cond
     ;; Path parameters are always strings (even if description mentions "list")
     ((or (string-match-p "\\bpath\\b" name-lower)
          (string-match-p "\\bfile\\b\\|\\bdirectory\\b\\|\\bfolder\\b" desc-lower))
      `(:type "string"))

     ;; Boolean types - check parameter name and description
     ((or (string-match-p "\\bbool\\b\\|\\btrue\\b\\|\\bfalse\\b" desc-lower)
          (string-match-p "whether\\|enable\\|disable\\|flag" desc-lower)
          (string-match-p "recursive\\|all\\|force" name-lower))
      `(:type "boolean"))

     ;; Number types
     ((or (string-match-p "\\bnumber\\b\\|\\binteger\\b\\|\\bcount\\b" desc-lower)
          (string-match-p "timeout\\|seconds\\|size\\|length\\|line.*number" desc-lower)
          (string-match-p "start.*line\\|end.*line\\|line" name-lower))
      `(:type "integer"))

     ;; Array/list types (but not paths!)
     ((and (not (string-match-p "\\bpath\\b" name-lower))
           (or (string-match-p "\\blist of\\b\\|\\barray of\\b\\|\\bedits\\b" desc-lower)
               (string-match-p "multiple\\|sequence\\|collection" desc-lower)
               (string-match-p "^list\\b" desc-lower))) ; "List of..." but not "list directory"
      (cond
       ;; List of edit operations (complex objects)
       ((string-match-p "edit.*operations\\|operations.*edit" desc-lower)
        `(:type "array"
          :items (:type "object"
                  :properties (:type (:type "string" :description "Type of edit operation")
                              :parameters (:type "object" :description "Parameters for the operation")))))
       ;; Generic list of strings
       (t `(:type "array" :items (:type "string")))))

     ;; Object types
     ((string-match-p "\\bobject\\b\\|\\bparameters\\b\\|\\bconfig\\b" desc-lower)
      `(:type "object"))

     ;; Default to string
     (t `(:type "string")))))

;; Helper function to convert tool definitions to Gemini's native format
(defun ai-auto-complete-gemini--convert-tools-to-native-format (allowed-tools)
  "Convert tool definitions to Gemini's native function calling format.
ALLOWED-TOOLS is a list of tool names that should be included."
  (let ((gemini-tools '()))
    (when allowed-tools
      (dolist (tool-name allowed-tools)
        (let ((tool (gethash tool-name ai-auto-complete-tools)))
          (when tool
            (let* ((description (plist-get tool :description))
                   (parameters (plist-get tool :parameters))
                   (gemini-properties '())
                   (required-params '()))

              ;; Convert parameters to Gemini's JSON schema format with type inference
              (dolist (param parameters)
                (let* ((param-name (car param))
                       (param-desc (cdr param))
                       (type-info (ai-auto-complete-gemini--infer-param-type param-name param-desc))
                       (param-type (plist-get type-info :type))
                       (param-schema `((type . ,param-type)
                                      (description . ,param-desc))))

                  ;; Add additional schema properties for complex types
                  (when (string= param-type "array")
                    (let ((items (plist-get type-info :items)))
                      (when items
                        (setq param-schema (append param-schema `((items . ,items)))))))

                  (when (string= param-type "object")
                    (let ((properties (plist-get type-info :properties)))
                      (when properties
                        (setq param-schema (append param-schema `((properties . ,properties)))))))

                  (push `(,(intern param-name) . ,param-schema) gemini-properties)

                  ;; For now, treat all parameters as required
                  ;; TODO: Could be enhanced to support optional parameters
                  (push param-name required-params)))

              ;; Create the Gemini function definition
              (let ((gemini-function
                     `((name . ,tool-name)
                       (description . ,description)
                       (parameters . ((type . "object")
                                      (properties . ,(nreverse gemini-properties))
                                      (required . ,(vconcat (nreverse required-params))))))))
                (push gemini-function gemini-tools))))))

    (when gemini-tools
      (message "DEBUG-GEMINI-TOOLS: Converted %d tools to native format with type inference" (length gemini-tools)))

    (nreverse gemini-tools))))

;; Helper function to detect if tool result is in structured format for native function calling
(defun ai-auto-complete-gemini--is-structured-tool-result (content)
  "Check if CONTENT is a structured tool result for native function calling.
Returns t if the content appears to be structured tool results, nil otherwise."
  (and (stringp content)
       (or (string-match-p "^\\[{.*}\\]$" (ai-auto-complete-gemini--trim content))  ; JSON array format
           (string-match-p "^{.*}$" (ai-auto-complete-gemini--trim content))         ; Single JSON object
           (string-match-p "STRUCTURED-TOOL-RESULTS:" content)))) ; Explicit marker

;; Helper function to parse structured tool results for native function calling
(defun ai-auto-complete-gemini--parse-structured-tool-results (content)
  "Parse structured tool results CONTENT into Gemini functionResponse format.
Returns a list of functionResponse objects."
  (let ((results '())
        (trimmed-content (ai-auto-complete-gemini--trim content)))

    (cond
     ;; Handle explicit structured format marker
     ((string-match-p "STRUCTURED-TOOL-RESULTS:" trimmed-content)
      (let ((json-part (replace-regexp-in-string "^.*STRUCTURED-TOOL-RESULTS:\\s-*" "" trimmed-content)))
        (condition-case err
            (let ((parsed-results (json-read-from-string json-part)))
              (if (vectorp parsed-results)
                  ;; Array of results
                  (dotimes (i (length parsed-results))
                    (let ((result (aref parsed-results i)))
                      (when (and (listp result) (assoc 'name result) (assoc 'response result))
                        (push `((name . ,(cdr (assoc 'name result)))
                               (response . ,(cdr (assoc 'response result))))
                              results))))
                ;; Single result
                (when (and (listp parsed-results) (assoc 'name parsed-results) (assoc 'response parsed-results))
                  (push `((name . ,(cdr (assoc 'name parsed-results)))
                         (response . ,(cdr (assoc 'response parsed-results))))
                        results))))
          (error
           (message "Error parsing structured tool results: %s" (error-message-string err))))))

     ;; Handle JSON array format
     ((string-match-p "^\\[{.*}\\]$" trimmed-content)
      (condition-case err
          (let ((parsed-array (json-read-from-string trimmed-content)))
            (when (vectorp parsed-array)
              (dotimes (i (length parsed-array))
                (let ((result (aref parsed-array i)))
                  (when (and (listp result) (assoc 'name result) (assoc 'response result))
                    (push `((name . ,(cdr (assoc 'name result)))
                           (response . ,(cdr (assoc 'response result))))
                          results))))))
        (error
         (message "Error parsing JSON array tool results: %s" (error-message-string err)))))

     ;; Handle single JSON object format
     ((string-match-p "^{.*}$" trimmed-content)
      (condition-case err
          (let ((parsed-object (json-read-from-string trimmed-content)))
            (when (and (listp parsed-object) (assoc 'name parsed-object) (assoc 'response parsed-object))
              (push `((name . ,(cdr (assoc 'name parsed-object)))
                     (response . ,(cdr (assoc 'response parsed-object))))
                    results)))
        (error
         (message "Error parsing JSON object tool results: %s" (error-message-string err)))))

     ;; Fallback: try to parse as XML and convert to structured format
     (t
      (let ((xml-results (ai-auto-complete-gemini--parse-internal-tool-result content)))
        (dolist (xml-result xml-results)
          (let ((tool-name (car xml-result))
                (tool-output (cdr xml-result)))
            (push `((name . ,tool-name)
                   (response . ((result . ,tool-output))))
                  results))))))

    (nreverse results)))

;; Helper function to parse the 'tool-result' string from history
;; This is a simplified parser; a more robust system would store structured tool data.
(defun ai-auto-complete-gemini--parse-internal-tool-result (tool-result-string)
  "Robustly parse a string potentially containing multiple <tool_result> XML blocks.
Returns a list of (cons tool-name . tool-content-string), or nil if input is empty."
  (let ((results '())
        (start 0)
        (trimmed-input (ai-auto-complete-gemini--trim tool-result-string))) ; Trim once at the beginning

    ;; If the input is empty after trimming, return nil immediately.
    (if (string-empty-p trimmed-input)
        nil
      (progn
        ;; Attempt to parse <tool_result...> tags
        ;; Regex updated for non-greedy multi-line content within tool_result
        (while (string-match "<tool_result name=\"\\([^\"]+\\)\">[[:space:]]*\\(\\(?:.\\|\n\\)*?\\)[[:space:]]*</tool_result>" trimmed-input start)
          (let ((tool-name (match-string 1 trimmed-input))
                (tool-content (ai-auto-complete-gemini--trim (match-string 2 trimmed-input)))) ; Content inside tool_result
            (push (cons tool-name tool-content) results))
          (setq start (match-end 0)))

        ;; If no <tool_result> tags were found by the primary regex,
        ;; and results list is still empty, try the simpler "Tool: name\nResult: content" format.
        ;; This part assumes only one such block if this format is used.
        (when (null results)
          (if (string-match "Tool: \\([^\n]+\\)\nResult: \\(\\(?:.\\|\n\\)*\\)" trimmed-input) ; Match multi-line result
              (push (cons (ai-auto-complete-gemini--trim (match-string 1 trimmed-input))
                          (ai-auto-complete-gemini--trim (match-string 2 trimmed-input)))
                    results)))
        
        ;; If still no results from specific parsers, and the input was not empty,
        ;; treat the whole original trimmed string as output of an "unknown_tool_operation".
        ;; This ensures that if there was content, we try to send something.
        (when (and (null results) (not (string-empty-p trimmed-input)))
            (push (cons "unknown_tool_operation" trimmed-input) results))
            
        (nreverse results))))) ; Return in the order they appeared, or nil if input was empty.



;; Gemini provider implementation
(defun ai-auto-complete-gemini-provider (history callback model system-prompt &optional agent-name)
  "Request completion from Gemini API with HISTORY, MODEL, SYSTEM-PROMPT, and optional AGENT-NAME."
  (message "Requesting completion from Gemini API with model %s" model)
  (message "Using system prompt: %s" (substring system-prompt 0 (length system-prompt)))
  (when agent-name
    (message "Request is for agent: %s" agent-name))
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (progn
        (message "Gemini API key is not set")
        (funcall callback "ERROR: Gemini API key is not set. Please set ai-auto-complete-gemini-api-key."))
    (let* (;; Use the model directly - it should already be the correct model name
           ;; from ai-auto-complete-get-correct-model-name in the backend
           (url (format "https://generativelanguage.googleapis.com/v1beta/models/%s:generateContent?key=%s" ; Using v1beta for systemInstruction
                        model ai-auto-complete-gemini-api-key))
           ;; Model attributes (temperature, max-tokens, etc.)
           (config-attrs (ai-auto-complete-apply-model-attributes 'gemini model (make-hash-table :test 'equal)))
           (temperature (or (gethash "temperature" config-attrs) 0.7))
           (max-tokens (or (gethash "maxOutputTokens" config-attrs) 1024))
           (top-p (or (gethash "topP" config-attrs) 0.9))
           (top-k (or (gethash "topK" config-attrs) 40))
             (generation-config `((temperature . ,temperature)
                                 (maxOutputTokens . ,max-tokens)
                                 (topP . ,top-p)
                                 (topK . ,top-k)))
           ;; Prepare systemInstruction
           (gemini-system-instruction
            (when (and system-prompt (not (string-empty-p system-prompt)))
              `((parts . [((text . ,system-prompt))]))))

           ;; Convert history to Gemini format directly
           (gemini-contents '())
           ;; Determine if native function calling should be used
           (use-native-tools (and (boundp 'ai-auto-complete-tools-enabled)
                                  ai-auto-complete-tools-enabled
                                  (fboundp 'ai-auto-complete-get-agent-tools)
                                  agent-name)))

      ;; Process history messages (reverse to get chronological order)
      (dolist (msg (reverse history))
        (pcase msg
          ;; User message
          (`(user . ,content)
           (push `((role . "user") (parts . [((text . ,content))])) gemini-contents))

          ;; Agent/Assistant message
          ;; this one is working, apparently, this is how we are adding response to history as 
          ;; agent
          (`(agent . (,name . ,content))
           (push `((role . "assistant") (parts . [((text . ,content))])) gemini-contents))

         ; (`(assistant . ,content)
          ; (push `((role . "assistant") (parts . [((text . ,content))])) gemini-contents))

          ;; Tool result - handle both native function calling and XML formats
          (`(tool-result . ,content)
           (if (and use-native-tools (ai-auto-complete-gemini--is-structured-tool-result content))
               ;; Native function calling: parse structured tool results
               (let ((function-responses (ai-auto-complete-gemini--parse-structured-tool-results content)))
                 (dolist (func-response function-responses)
                   (let ((func-name (cdr (assoc 'name func-response)))
                         (func-result (cdr (assoc 'response func-response))))
                     (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                       (message "[GEMINI-DEBUG] Creating function response: name=%s, result=%s"
                                func-name (json-encode func-result)))
                     (push `((role . "function")
                            (parts . [((functionResponse . ((name . ,func-name)
                                                           (response . ,func-result))))]))
                           gemini-contents))))
             ;; Fallback: XML format or plain text
             (push `((role . "model") (parts . [((text . ,(format "Tool result: %s" content)))])) gemini-contents)))

          ;; Unknown format
          (_ (message "Warning: Unknown history format: %S" msg))))

      ;; Reverse to get correct chronological order
      (setq gemini-contents (nreverse gemini-contents))
      (message "DEBUG-SIMPLE: Converted %d history messages to %d Gemini contents" (length history) (length gemini-contents))

      ;; Debug the final contents
      (message "DEBUG-SIMPLE: Final Gemini contents: %s" (json-encode gemini-contents))

      (let (;; Prepare native function calling tools if tools are enabled
           (gemini-native-tools nil)
           (payload `((contents . ,(vconcat gemini-contents))
                      (generationConfig . ,generation-config))))

      ;; Add systemInstruction to payload if it exists
      (when gemini-system-instruction
        (setq payload (append payload `((systemInstruction . ,gemini-system-instruction)))))

      ;; Add native function calling tools if enabled AND no function responses are being sent in this turn
      (let ((contains-function-response-p
             (cl-some (lambda (content-item)
                        (equal (cdr (assoc 'role content-item)) "function"))
                      gemini-contents)))
        (when (and use-native-tools (not contains-function-response-p))
          (let ((allowed-tools (ai-auto-complete-get-agent-tools agent-name)))
            (when allowed-tools
              (setq gemini-native-tools (ai-auto-complete-gemini--convert-tools-to-native-format allowed-tools))
              (when gemini-native-tools
                (message "DEBUG-GEMINI-TOOLS: Adding %d native tools for agent %s (no function response in this turn)"
                         (length gemini-native-tools) agent-name)
                (setq payload (append payload `((tools . [((functionDeclarations . ,(vconcat gemini-native-tools)))]))))
                ;; Add tool configuration for automatic function calling
                (setq payload (append payload `((toolConfig . ((functionCallingConfig . ((mode . "AUTO"))))))))))))

      (let ((data (json-encode payload)))
        (message "Gemini request payload (condensed): %s"
                 (substring data 0 (min 200 (length data))))
        ;; Moved debug messages inside the scope of contains-function-response-p
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          ;; This message was already inside the correct scope in your previous version, but good to double check
          (message "[GEMINI-DEBUG] Payload includes tools/toolConfig: %s" (not contains-function-response-p)))
        ;; Additional debugging for function responses
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[GEMINI-DEBUG] Full payload size: %d characters" (length data))
          (let ((function-count (cl-count-if (lambda (content)
                                              (equal (cdr (assoc 'role content)) "function"))
                                            gemini-contents)))
            (when (> function-count 0)
              (message "[GEMINI-DEBUG] Payload contains %d function response messages" function-count)
              ;; Show the function response parts specifically
              (dolist (content gemini-contents)
                (when (equal (cdr (assoc 'role content)) "function")
                  (let ((parts (cdr (assoc 'parts content))))
                    (when (vectorp parts)
                      (dotimes (i (length parts))
                        (let* ((part (aref parts i))
                               (func-response (cdr (assoc 'functionResponse part))))
                          (when func-response
                            (message "[GEMINI-DEBUG] Function response part: %s"
                                     (json-encode func-response))))))))))))
        ;; Additional debugging for tool-related requests
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "DEBUG-GEMINI-REQUEST: Full payload structure:")
          (message "  - Contents count: %d" (length gemini-contents))
          (message "  - Has systemInstruction: %s" (if gemini-system-instruction "yes" "no"))
          (message "  - Has native tools: %s" (if gemini-native-tools "yes" "no"))
          (message "  - Payload keys: %s" (mapcar #'car payload))
          ;; Check for function role messages
          (let ((function-messages (cl-count-if (lambda (content)
                                                  (equal (cdr (assoc 'role content)) "function"))
                                                gemini-contents)))
            (when (> function-messages 0)
              (message "DEBUG-GEMINI-REQUEST: Found %d function role messages" function-messages))))
        (setq ai-auto-complete--pending-request t)
        (ai-auto-complete--update-mode-line)
        (request url :type "POST" :headers '(("Content-Type" . "application/json"))
                 :data data :parser 'json-read
               :success (lambda (&rest args)
                         (let* ((response-data (plist-get args :data)))
                           ;; Check if the response contains an error
                           (if (assoc 'error response-data)
                               (let* ((error-obj (cdr (assoc 'error response-data)))
                                      (error-msg (cdr (assoc 'message error-obj))))
                                 (message "Gemini API error message: %s" error-msg)
                                 (funcall callback (format "ERROR: %s" error-msg)))
                             ;; Normal response processing
                             (let* ((candidates (cdr (assoc 'candidates response-data)))
                                    (first-candidate (when (and candidates (> (length candidates) 0)) (aref candidates 0)))
                                    (content (when first-candidate (cdr (assoc 'content first-candidate))))
                                    (parts (when content (cdr (assoc 'parts content))))
                                    (first-part (when (and parts (> (length parts) 0)) (aref parts 0)))
                                    ;; Check for functionCall first
                                    (function-call-data (when first-part (cdr (assoc 'functionCall first-part))))
                                    (text-data (when first-part (cdr (assoc 'text first-part))))
                                    (final-response-text (or text-data "")))

                               ;; Pass the agent-name to the tools processing function
                               ;; Check for native function calls in all parts (Gemini can return multiple function calls)
                               (let ((function-calls '())
                                     (text-parts '()))
                                 ;; Collect all function calls and text parts
                                 (when parts
                                   (dotimes (i (length parts))
                                     (let* ((part (aref parts i))
                                            (function-call-data (cdr (assoc 'functionCall part)))
                                            (text-data (cdr (assoc 'text part))))
                                       (when function-call-data
                                         (push function-call-data function-calls))
                                       (when text-data
                                         (push text-data text-parts)))))

                                 ;; Process function calls if any exist
                                 (if function-calls
                                     (progn
                                       ;; Convert all function calls to XML format for existing tools system
                                       (let ((xml-tool-calls '()))
                                         (dolist (func-call (nreverse function-calls))
                                           (let* ((tool-name (cdr (assoc 'name func-call)))
                                                  (tool-args-json (json-encode (cdr (assoc 'args func-call))))
                                                  (xml-tool-call (format "<tool name=\"%s\"><parameters>%s</parameters></tool>"
                                                                         tool-name tool-args-json)))
                                             (push xml-tool-call xml-tool-calls)))

                                         (let ((combined-xml (string-join (nreverse xml-tool-calls) "\n")))
                                           (message "Gemini returned %d native function call(s), processing as tools: %s"
                                                    (length function-calls) combined-xml)
                                           (ai-auto-complete-tools-process-response combined-xml callback agent-name))))
                                 ;; Else, it's a text response or a text response after a tool was already processed
                                 (if (and (boundp 'ai-auto-complete-tools-enabled)
                                          ai-auto-complete-tools-enabled
                                          (fboundp 'ai-auto-complete-tools-process-response)
                                          (string-match-p "<tool name=" final-response-text)) ; Check for manually inserted tool calls
                                     (progn
                                       (message "Processing Gemini text response for XML tools with agent-name: %s" (or agent-name "nil"))
                                       (ai-auto-complete-tools-process-response final-response-text callback agent-name))
                                   (funcall callback final-response-text)))))))
               :error (lambda (&rest args)
                       (let* ((request-error (plist-get args :error))
                              (response (plist-get args :response))
                              (error-data (plist-get args :data)))
                          (message "Error in Gemini provider - Full error details:")
                          (message "Error: %S" request-error)
                          (message "Response: %S" response)
                          (message "Error Data: %S" error-data)
                          ;; Additional debugging for tool-related errors
                          (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                            (message "DEBUG-GEMINI-ERROR: Request contained %d contents" (length gemini-contents))
                            (message "DEBUG-GEMINI-ERROR: Request payload size: %d characters" (length data))
                            ;; Check if this was a tool-related request
                            (let ((function-messages (cl-count-if (lambda (content)
                                                                    (equal (cdr (assoc 'role content)) "function"))
                                                                  gemini-contents)))
                              (when (> function-messages 0)
                                (message "DEBUG-GEMINI-ERROR: This request contained %d function response messages" function-messages))))
                          (funcall callback (format "ERROR: %s\nResponse: %S\nData: %S"
                                                  request-error response error-data))))
                 ))))))))
;; Test functions for Gemini API

(defun ai-auto-complete-check-gemini-api-key ()
  "Check if the Gemini API key is valid by listing available models."
  (interactive)
  (message "Checking Gemini API key...")
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let ((url (format "https://generativelanguage.googleapis.com/v1/models?key=%s"
                      ai-auto-complete-gemini-api-key)))
      (message "Requesting models list from: %s" url)
      (request url
               :type "GET"
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Gemini API key is valid!")
                           (let* ((models (cdr (assoc 'models data)))
                                  (model-names (mapcar (lambda (model)
                                                        (cdr (assoc 'name model)))
                                                      models)))
                             (message "Available Gemini models: %S" model-names))))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Gemini API key validation ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Your Gemini API key appears to be invalid or has insufficient permissions.")))))
    t))

(defun ai-auto-complete-test-gemini-api ()
  "Test the Gemini API with a simple request and display detailed results."
  (interactive)
  (message "Testing Gemini API...")
  (message "API Key set: %s" (not (string-empty-p ai-auto-complete-gemini-api-key)))
  (let* ((model "gemini-2.0-flash-lite")
         (test-prompt "What is the capital of France?")
         ;; Use the model directly
         (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                     model
                     ai-auto-complete-gemini-api-key))
         (config-attrs (ai-auto-complete-apply-model-attributes 'gemini model (make-hash-table :test 'equal)))
         (temperature (or (and config-attrs (gethash "temperature" config-attrs)) 0.7))
         (max-tokens (or (and config-attrs (gethash "maxOutputTokens" config-attrs)) 1024))
         (top-p (or (and config-attrs (gethash "topP" config-attrs)) 0.9))
         (top-k (or (and config-attrs (gethash "topK" config-attrs)) 40))
         (generation-config `((temperature . ,temperature)
                             (maxOutputTokens . ,max-tokens)
                             (topP . ,top-p)
                             (topK . ,top-k)))
         (data (json-encode `((contents . [((role . "user")
                                         (parts . [((text . ,test-prompt))]))])
                            (generationConfig . ,generation-config)))))
    (message "Testing URL: %s" url)
    (message "Request data: %s" data)
    (request url
             :type "POST"
             :headers '(("Content-Type" . "application/json"))
             :data data
             :parser 'json-read
             :success (cl-function
                       (lambda (&key data &allow-other-keys)
                         (message "Gemini API test SUCCESS")
                         (message "Response data: %S" data)
                         (let* ((candidates (cdr (assoc 'candidates data)))
                                (first-candidate (aref candidates 0))
                                (content (cdr (assoc 'content first-candidate)))
                                (parts (cdr (assoc 'parts content)))
                                (first-part (aref parts 0))
                                (text (cdr (assoc 'text first-part))))
                           (message "Generated text: %s" text))))
             :error (cl-function
                     (lambda (&key error-thrown response data &allow-other-keys)
                       (message "Gemini API test ERROR: %S" error-thrown)
                       (when response
                         (message "Response status: %s" (request-response-status-code response))
                         (message "Response headers: %s" (request-response-headers response))
                         (message "Response data: %s" (request-response-data response)))
                       (message "Full error details: %S" (list :error error-thrown :response response :data data)))))))

(defun ai-auto-complete-test-gemini-model (model)
  "Test if a specific Gemini MODEL is available and working."
  (interactive
   (list (read-string "Enter Gemini model name to test: " "gemini-2.0-flash-lite")))
  (message "Testing Gemini model: %s" model)
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let* (;; Use the model directly
           (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                      model
                      ai-auto-complete-gemini-api-key))
           (test-prompt "What is the capital of France?")
           (data (json-encode `((contents . [((role . "user")
                                           (parts . [((text . ,test-prompt))]))])
                              (generationConfig . ((temperature . 0.7)
                                                  (maxOutputTokens . 1024)
                                                  (topP . 0.9)
                                                  (topK . 40)))))))
      (message "Testing URL: %s" url)
      (request url
               :type "POST"
               :headers '(("Content-Type" . "application/json"))
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Model %s is working correctly!" model)
                           (message "Response received successfully")))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Model test ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Model %s appears to be invalid or inaccessible" model)))))))

(defun ai-auto-complete-test-gemini-chat ()
  "Test the Gemini API with a chat request format."
  (interactive)
  (message "Testing Gemini API with chat format...")
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let* ((model "gemini-2.0-flash-lite")
           ;; Use the model directly
           (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                       model
                       ai-auto-complete-gemini-api-key))
           (system-prompt "You are a helpful AI assistant.")
           (user-message "What is the capital of France?")
           ;; Create a chat-like message structure - prepend system prompt to user message
           (messages (list
                      `((role . "user")
                        (parts . [((text . ,(concat "System: " system-prompt "\n\nUser: " user-message)))]))))
           (data (json-encode `((contents . ,(vconcat [] messages))
                              (generationConfig . ((temperature . 0.7)
                                                  (maxOutputTokens . 1024)
                                                  (topP . 0.9)
                                                  (topK . 40)))))))
      (message "Testing URL: %s" url)
      (message "Request data: %s" data)
      (request url
               :type "POST"
               :headers '(("Content-Type" . "application/json"))
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Gemini chat test SUCCESS")
                           (message "Response data: %S" data)
                           (let* ((candidates (cdr (assoc 'candidates data)))
                                  (first-candidate (aref candidates 0))
                                  (content (cdr (assoc 'content first-candidate)))
                                  (parts (cdr (assoc 'parts content)))
                                  (first-part (aref parts 0))
                                  (text (cdr (assoc 'text first-part))))
                             (message "Generated text: %s" text))))
               :error (cl-function
                       (lambda (&key error-thrown response data &allow-other-keys)
                         (message "Gemini chat test ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response headers: %s" (request-response-headers response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Full error details: %S" (list :error error-thrown :response response :data data))))))))

(defun ai-auto-complete-debug-gemini-chat-history ()
  "Debug the Gemini chat history format."
  (interactive)
  (if (not (and (boundp 'ai-auto-complete--chat-history)
                (not (null ai-auto-complete--chat-history))))
      (message "No chat history available. Please start a chat first.")
    (let* ((model "gemini-2.0-flash-lite")
           (system-prompt "You are a helpful AI assistant.")
           (context "Hello")
           (messages nil))
      ;; Add current message with system prompt prepended
      (push `((role . "user")
             (parts . [((text . ,(concat "System: " system-prompt "\n\nUser: " context)))])) messages)
      ;; Add history messages (oldest to newest)
      (dolist (msg ai-auto-complete--chat-history) ;; No need to reverse, we want oldest first
        (let ((role (cond ((eq (car msg) 'user) "user")
                         ((eq (car msg) 'agent) "model")
                         ((eq (car msg) 'tool-result) "model")
                         (t "model")))
              (content (cond
                        ((eq (car msg) 'agent) (cdr (cdr msg))) ; Extract the actual message content from agent response
                        ((eq (car msg) 'tool-result) (format "Tool Results: %s" (cdr msg))) ; Format tool results
                        (t (cdr msg)))))
          (message "Chat history entry - Role: %s, Content: %s" role
                   (if (stringp content)
                       (substring content 0 (min 30 (length content)))
                     "<non-string content>"))
          (push `((role . ,role)
                 (parts . [((text . ,content))])) messages)))

      (let* ((url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                        model
                        ai-auto-complete-gemini-api-key))
             (data (json-encode `((contents . ,(vconcat [] (reverse messages)))
                                (generationConfig . ((temperature . 0.7)
                                                    (maxOutputTokens . 1024)
                                                    (topP . 0.9)
                                                    (topK . 40)))))))
        (message "Chat history debug - URL: %s" url)
        (message "Chat history debug - Request data: %s" data)
        (message "Chat history debug - Total messages: %d" (length messages))
        (request url
                 :type "POST"
                 :headers '(("Content-Type" . "application/json"))
                 :data data
                 :parser 'json-read
                 :success (cl-function
                           (lambda (&key data &allow-other-keys)
                             (message "Chat history debug - SUCCESS")
                             (message "Chat history debug - Response received successfully")))
                 :error (cl-function
                         (lambda (&key error-thrown response data &allow-other-keys)
                           (message "Chat history debug - ERROR: %S" error-thrown)
                           (when response
                             (message "Chat history debug - Response status: %s" (request-response-status-code response))
                             (message "Chat history debug - Response data: %s" (request-response-data response)))
                           (message "Chat history debug - Full error details: %S" (list :error error-thrown :response response :data data)))))))))

(defun ai-auto-complete-test-gemini-model-variants ()
  "Test different Gemini model name variants to find which ones work."
  (interactive)
  (message "Testing different Gemini model variants...")
  (let ((models '("gemini-2.0-flash-lite"
                  "gemini-2.0-flash"
                  "gemini-2.0-pro"
                  "gemini-2.5-flash-preview"
                  "gemini-2.5-pro-preview-03-25"
                  "gemini-2.5-pro-exp-03-25"
                  "gemini-pro"
                  "gemini-pro-vision"
                  "gemini-1.5-pro"
                  "gemini-1.5-flash")))
    (dolist (model models)
      (message "Testing model variant: %s" model)
      (let* (;; Use the model directly
             (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                        model
                        ai-auto-complete-gemini-api-key))
             (test-prompt "What is the capital of France?")
             (data (json-encode `((contents . [((role . "user")
                                             (parts . [((text . ,test-prompt))]))])
                                (generationConfig . ((temperature . 0.7)
                                                    (maxOutputTokens . 1024)
                                                    (topP . 0.9)
                                                    (topK . 40)))))))
        (request url
                 :type "POST"
                 :headers '(("Content-Type" . "application/json"))
                 :data data
                 :parser 'json-read
                 :success (cl-function
                           (lambda (&key data &allow-other-keys)
                             (message "Model %s: SUCCESS" model)))
                 :error (cl-function
                         (lambda (&key error-thrown response &allow-other-keys)
                           (message "Model %s: ERROR - %s" model error-thrown)
                           (when response
                             (message "Model %s: Status %s" model (request-response-status-code response))))))))))

(defun ai-auto-complete-test-gemini-native-tools-standalone ()
  "Test Gemini native function calling conversion with mock data."
  (interactive)
  (message "Testing Gemini native function calling conversion...")

  ;; Create mock tool definitions to test conversion
  (let ((mock-tools (make-hash-table :test 'equal)))
    ;; Add mock read_file tool
    (puthash "read_file"
             (list :description "Reads the entire content of a specified file and returns it as a string"
                   :function nil  ; Not needed for conversion test
                   :parameters '(("path" . "The absolute or relative path to the file whose content is to be read")))
             mock-tools)

    ;; Add mock write_file tool
    (puthash "write_file"
             (list :description "Writes the provided content to a specified file"
                   :function nil  ; Not needed for conversion test
                   :parameters '(("path" . "The absolute or relative path to the file where the content will be written")
                                ("content" . "The textual content to be written into the file")))
             mock-tools)

    ;; Add mock run_command tool
    (puthash "run_command"
             (list :description "Executes a given shell command and returns its standard output and exit code"
                   :function nil  ; Not needed for conversion test
                   :parameters '(("command" . "The shell command string to be executed")))
             mock-tools)

    ;; Add mock tool with complex parameters to test type inference
    (puthash "str_replace_editor"
             (list :description "Tool for editing files with structured operations"
                   :function nil  ; Not needed for conversion test
                   :parameters '(("path" . "Path to the file to modify")
                                ("edits" . "List of edit operations, each with 'type' and parameters")
                                ("timeout" . "Timeout in seconds for the operation")
                                ("enable_backup" . "Whether to create a backup before editing")))
             mock-tools)

    ;; Temporarily replace the global tools hash table for testing
    (let ((original-tools ai-auto-complete-tools))
      (setq ai-auto-complete-tools mock-tools)

      (unwind-protect
          (progn
            ;; Test the conversion function
            (let* ((test-tools '("read_file" "write_file" "run_command" "str_replace_editor"))
                   (converted-tools (ai-auto-complete-gemini--convert-tools-to-native-format test-tools)))

              (message "=== GEMINI NATIVE FUNCTION CALLING TEST WITH TYPE INFERENCE ===")
              (message "Input tools: %s" test-tools)
              (message "Number of converted tools: %d" (length converted-tools))
              (message "")

              ;; Display each converted tool with type information
              (dolist (tool converted-tools)
                (let ((name (cdr (assoc 'name tool)))
                      (description (cdr (assoc 'description tool)))
                      (parameters (cdr (assoc 'parameters tool))))
                  (message "Tool: %s" name)
                  (message "  Description: %s" description)
                  (message "  Parameters schema:")

                  ;; Show parameter types
                  (let ((properties (cdr (assoc 'properties parameters))))
                    (dolist (prop properties)
                      (let* ((param-name (car prop))
                             (param-schema (cdr prop))
                             (param-type (cdr (assoc 'type param-schema)))
                             (param-desc (cdr (assoc 'description param-schema))))
                        (message "    %s: %s (%s)" param-name param-type param-desc))))
                  (message "")))

              ;; Show the complete JSON structure
              (message "Complete Gemini tools JSON:")
              (message "%s" (json-encode converted-tools))
              (message "")
              (message "=== TYPE INFERENCE TEST COMPLETED SUCCESSFULLY ===")

              ;; Return the converted tools for further inspection
              converted-tools))

        ;; Restore original tools hash table
        (setq ai-auto-complete-tools original-tools)))))

(defun ai-auto-complete-test-gemini-native-tools ()
  "Test Gemini native function calling with a simple tool."
  (interactive)
  (message "Testing Gemini native function calling...")

  ;; First, ensure we have some tools registered
  (unless (> (hash-table-count ai-auto-complete-tools) 0)
    (message "No tools registered. Please load tools first.")
    (return))

  ;; Test the tool conversion function
  (let* ((test-tools '("read_file" "write_file"))  ; Common tools
         (converted-tools (ai-auto-complete-gemini--convert-tools-to-native-format test-tools)))
    (message "Test: Converting tools to native format")
    (message "Input tools: %s" test-tools)
    (message "Converted tools: %s" (json-encode converted-tools)))

  ;; Test with actual agent tools if available
  (when (fboundp 'ai-auto-complete-get-agent-tools)
    (let* ((agent-name "chat")  ; Use a common agent
           (agent-tools (ai-auto-complete-get-agent-tools agent-name))
           (converted-agent-tools (ai-auto-complete-gemini--convert-tools-to-native-format agent-tools)))
      (message "Test: Converting agent tools to native format")
      (message "Agent: %s" agent-name)
      (message "Agent tools: %s" agent-tools)
      (message "Converted agent tools: %s" (json-encode converted-agent-tools)))))

(defun ai-auto-complete-test-gemini-type-inference ()
  "Test the Gemini parameter type inference function."
  (interactive)
  (message "Testing Gemini parameter type inference...")

  ;; Test cases for type inference
  (let ((test-cases '(("path" . "The absolute or relative path to the file")
                      ("path" . "Path to the directory to list")  ; This was causing the issue
                      ("edits" . "List of edit operations, each with 'type' and parameters")
                      ("timeout" . "Timeout in seconds for the operation")
                      ("enable_backup" . "Whether to create a backup before editing")
                      ("recursive" . "Boolean: true for recursive listing, false or omitted for non-recursive")
                      ("start_line" . "Starting line number to delete (1-indexed)")
                      ("count" . "Number of items to process")
                      ("config" . "Configuration object with settings")
                      ("items" . "Array of items to process")
                      ("content" . "The textual content to be written"))))

    (message "=== GEMINI TYPE INFERENCE TEST ===")
    (dolist (test-case test-cases)
      (let* ((param-name (car test-case))
             (param-desc (cdr test-case))
             (type-info (ai-auto-complete-gemini--infer-param-type param-name param-desc))
             (inferred-type (plist-get type-info :type)))
        (message "Parameter: %s" param-name)
        (message "  Description: %s" param-desc)
        (message "  Inferred type: %s" inferred-type)
        (when (plist-get type-info :items)
          (message "  Items schema: %s" (plist-get type-info :items)))
        (when (plist-get type-info :properties)
          (message "  Properties schema: %s" (plist-get type-info :properties)))
        (message "")))

    (message "=== TYPE INFERENCE TEST COMPLETED ===")))

(defun ai-auto-complete-test-gemini-native-function-calling-integration ()
  "Test the complete Gemini native function calling integration."
  (interactive)
  (message "=== TESTING GEMINI NATIVE FUNCTION CALLING INTEGRATION ===")

  ;; Test structured tool result detection
  (let ((test-cases '(
                      ;; Test structured format with explicit marker
                      ("STRUCTURED-TOOL-RESULTS: [{\"name\":\"read_file\",\"response\":{\"output\":\"file content\"}}]" . t)
                      ;; Test JSON array format
                      ("[{\"name\":\"read_file\",\"response\":{\"output\":\"file content\"}}]" . t)
                      ;; Test single JSON object
                      ("{\"name\":\"read_file\",\"response\":{\"output\":\"file content\"}}" . t)
                      ;; Test XML format (should be false)
                      ("<tool_result name=\"read_file\">file content</tool_result>" . nil)
                      ;; Test plain text (should be false)
                      ("This is just plain text" . nil))))

    (message "Testing structured tool result detection:")
    (dolist (test-case test-cases)
      (let* ((content (car test-case))
             (expected (cdr test-case))
             (actual (ai-auto-complete-gemini--is-structured-tool-result content)))
        (message "  Content: %s" (substring content 0 (min 50 (length content))))
        (message "  Expected: %s, Actual: %s, Result: %s"
                 expected actual (if (eq expected actual) "PASS" "FAIL"))))

    (message "")
    (message "Testing structured tool result parsing:")

    ;; Test parsing structured results
    (let ((test-content "STRUCTURED-TOOL-RESULTS: [{\"name\":\"read_file\",\"response\":{\"output\":\"Hello World\"}},{\"name\":\"write_file\",\"response\":{\"output\":\"File written successfully\"}}]"))
      (message "  Test content: %s" test-content)
      (let ((parsed-results (ai-auto-complete-gemini--parse-structured-tool-results test-content)))
        (message "  Parsed %d results:" (length parsed-results))
        (dolist (result parsed-results)
          (let ((name (cdr (assoc 'name result)))
                (response (cdr (assoc 'response result))))
            (message "    Tool: %s, Response: %s" name response)))))

    (message "")
    (message "=== GEMINI NATIVE FUNCTION CALLING INTEGRATION TEST COMPLETED ===")))

(defun ai-auto-complete-test-gemini-contents-fix ()
  "Test the simplified ai-auto-complete-gemini--build-contents-from-history function."
  (interactive)
  (message "Testing Gemini contents fix...")

  ;; Test case 1: Simple history with user and agent messages
  (let* ((test-history '((user . "Hello") (agent . ("chat" . "Hi there!"))))
         (result (ai-auto-complete-gemini--build-contents-from-history test-history "chat")))
    (message "Test 1 - Simple history:")
    (message "Input history: %s" test-history)
    (message "Result: %s" (json-encode result)))

  ;; Test case 2: History with tool calls
  (let* ((test-history '((user . "What files are in the current directory?")
                         (agent . ("chat" . "<tool name=\"list_files\"><parameters>{\"path\": \".\"}</parameters></tool>"))
                         (tool-result . "<tool_result name=\"list_files\">file1.txt\nfile2.py</tool_result>")
                         (agent . ("chat" . "I found two files: file1.txt and file2.py"))))
         (result (ai-auto-complete-gemini--build-contents-from-history test-history "chat")))
    (message "Test 2 - History with tool calls:")
    (message "Input history length: %d" (length test-history))
    (message "Result: %s" (json-encode result)))

  ;; Test case 3: Empty history
  (let* ((test-history '())
         (result (ai-auto-complete-gemini--build-contents-from-history test-history "chat")))
    (message "Test 3 - Empty history:")
    (message "Input history: %s" test-history)
    (message "Result: %s" (json-encode result))))

;; Register the provider
(ai-auto-complete-register-provider 'gemini #'ai-auto-complete-gemini-provider)

(defun ai-auto-complete-test-gemini-conversation-history-structure ()
  "Test the Gemini API payload structure with a mock conversation history that includes tool calls.
This verifies that the conversation history fix produces the correct API payload structure."
  (interactive)
  (message "=== TESTING GEMINI CONVERSATION HISTORY STRUCTURE ===")

  ;; Create mock conversation history that simulates the fixed flow
  (let* ((mock-history '(
                        ;; User's original request
                        (user . "list out the files in the current directory")
                        ;; Agent's response with tool call (this is what the fix adds)
                        (agent . ("linux" . "I'll help you list the files in the current directory.\n\n<tool name=\"run_command\">\n<parameters>{\"command\": \"ls -la\"}</parameters>\n</tool>"))
                        ;; Tool result (this was already being added)
                        (tool-result . "STRUCTURED-TOOL-RESULTS: [{\"name\":\"run_command\",\"response\":{\"result\":\"total 48\\ndrwxr-xr-x 3 <USER> <GROUP> 4096 Jan  1 12:00 .\\ndrwxr-xr-x 5 <USER> <GROUP> 4096 Jan  1 12:00 ..\\n-rw-r--r-- 1 <USER> <GROUP> 1234 Jan  1 12:00 file1.txt\\n-rw-r--r-- 1 <USER> <GROUP> 5678 Jan  1 12:00 file2.py\"}}]")))
         (agent-name "linux")
         (model "gemini-2.0-flash-lite")
         (system-prompt "You are a helpful Linux assistant.")
         (use-native-tools t))

    (message "[TEST] Mock history has %d messages:" (length mock-history))
    (let ((count 0))
      (dolist (hist-msg mock-history)
        (setq count (1+ count))
        (message "[TEST] History item %d - Role: %s" count (car hist-msg))))

    ;; Test the Gemini history conversion
    (message "[TEST] Converting history to Gemini format...")
    (let ((gemini-contents '()))

      ;; Process each history message (simulate the conversion logic)
      (dolist (msg (reverse mock-history)) ; Reverse to get chronological order
        (pcase msg
          ;; User message
          (`(user . ,content)
           (push `((role . "user") (parts . [((text . ,content))])) gemini-contents))

          ;; Agent/Assistant message
          (`(agent . (,name . ,content))
           (push `((role . "assistant") (parts . [((text . ,content))])) gemini-contents))

          ;; Tool result with structured format
          (`(tool-result . ,content)
           (if (and use-native-tools (string-prefix-p "STRUCTURED-TOOL-RESULTS:" content))
               ;; Parse structured tool results
               (let* ((json-str (substring content (length "STRUCTURED-TOOL-RESULTS: ")))
                      (function-responses (json-read-from-string json-str)))
                 (when (vectorp function-responses)
                   (dotimes (i (length function-responses))
                     (let* ((func-response (aref function-responses i))
                            (func-name (cdr (assoc 'name func-response)))
                            (func-result (cdr (assoc 'response func-response))))
                       (push `((role . "function")
                              (parts . [((functionResponse . ((name . ,func-name)
                                                             (response . ,func-result))))]))
                             gemini-contents)))))
             ;; Fallback for non-structured format
             (push `((role . "model") (parts . [((text . ,(format "Tool result: %s" content)))])) gemini-contents)))))

      ;; Show the final structure
      (message "[TEST] Converted to %d Gemini contents:" (length gemini-contents))
      (let ((count 0))
        (dolist (content gemini-contents)
          (setq count (1+ count))
          (let ((role (cdr (assoc 'role content))))
            (message "[TEST] Gemini content %d - Role: %s" count role)
            (when (equal role "function")
              (let* ((parts (cdr (assoc 'parts content)))
                     (first-part (when (vectorp parts) (aref parts 0)))
                     (func-response (when first-part (cdr (assoc 'functionResponse first-part))))
                     (func-name (when func-response (cdr (assoc 'name func-response)))))
                (message "[TEST]   Function name: %s" func-name))))))

      ;; Verify the expected sequence
      (let ((roles (mapcar (lambda (content) (cdr (assoc 'role content))) gemini-contents)))
        (message "[TEST] Role sequence: %s" roles)
        (if (equal roles '("user" "assistant" "function"))
            (message "[TEST] ✓ CORRECT: Sequence is user -> assistant -> function")
          (message "[TEST] ✗ INCORRECT: Expected user -> assistant -> function, got %s" roles)))

      (message "[TEST] This structure should prevent the 400 Bad Request error from Gemini API"))))

(provide 'providers/gemini)
;;; gemini.el ends here
