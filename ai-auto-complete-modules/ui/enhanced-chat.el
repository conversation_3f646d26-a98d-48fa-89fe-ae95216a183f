;;; enhanced-chat.el --- Enhanced chat interface for AI Auto Complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides an enhanced chat interface for AI Auto Complete.
;; It adds support for streaming, markdown rendering, and interactive elements.

;;; Code:

(require 'cl-lib)
(require 'ui/markdown-renderer)
(require 'ui/streaming)
(require 'chat-customization) ; For buffer names

;; Customization options for the enhanced chat interface
(defgroup ai-auto-complete-enhanced-chat nil
  "Settings for the enhanced chat interface in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-enhanced-chat-")

(defcustom ai-auto-complete-enhanced-chat-show-timestamps t
  "Whether to show timestamps for messages and events."
  :type 'boolean
  :group 'ai-auto-complete-enhanced-chat)

(defcustom ai-auto-complete-enhanced-chat-show-tool-calls t
  "Whether to show tool calls in the chat interface."
  :type 'boolean
  :group 'ai-auto-complete-enhanced-chat)

(defcustom ai-auto-complete-enhanced-chat-show-tool-results t
  "Whether to show tool results in the chat interface."
  :type 'boolean
  :group 'ai-auto-complete-enhanced-chat)

(defcustom ai-auto-complete-enhanced-chat-show-agent-controls t
  "Whether to show agent controls in the chat interface."
  :type 'boolean
  :group 'ai-auto-complete-enhanced-chat)

;; Define faces for the enhanced chat interface
(defface ai-auto-complete-enhanced-chat-timestamp-face
  '((t :foreground "#888a85" :slant italic :height 0.8))
  "Face for timestamps in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-tool-face
  '((t :foreground "#ad7fa8" :weight bold))
  "Face for tool calls in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-tool-result-face
  '((t :foreground "#729fcf" :weight bold))
  "Face for tool results in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-agent-face
  '((t :foreground "#fcaf3e" :weight bold))
  "Face for agent messages in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-button-face
  '((t :box t :foreground "#729fcf" :background "#eeeeec"))
  "Face for buttons in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-header-face
  '((t :height 1.2 :weight bold :foreground "#3465a4" :background "#eeeeec"))
  "Face for the header in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

;; Variables for tracking the enhanced chat state
(defvar-local ai-auto-complete-enhanced-chat-mode nil
  "Mode variable for the enhanced chat interface.")

(defvar-local ai-auto-complete-enhanced-chat-input-marker nil
  "Marker for the current input position in the enhanced chat buffer.")

(defvar-local ai-auto-complete-enhanced-chat-header-marker nil
  "Marker for the header section in the enhanced chat buffer.")

(defvar-local ai-auto-complete-enhanced-chat-content-marker nil
  "Marker for the content section in the enhanced chat buffer.")

(defvar-local ai-auto-complete-enhanced-chat-footer-marker nil
  "Marker for the footer section in the enhanced chat buffer.")

(defvar-local ai-auto-complete-enhanced-chat-active-agent nil
  "The currently active agent in the enhanced chat interface.")

(defvar-local ai-auto-complete-enhanced-chat-available-agents nil
  "List of available agents in the enhanced chat interface.")

;; Define the enhanced chat mode map
(defvar ai-auto-complete-enhanced-chat-mode-map
  (let ((map (make-sparse-keymap)))
    (define-key map (kbd "RET") 'ai-auto-complete-enhanced-chat-send-message)
    (define-key map (kbd "C-c C-c") 'ai-auto-complete-enhanced-chat-cancel)
    (define-key map (kbd "C-c C-k") 'ai-auto-complete-enhanced-chat-clear)
    (define-key map (kbd "C-c C-s") 'ai-auto-complete-enhanced-chat-save-session)
    (define-key map (kbd "C-c C-l") 'ai-auto-complete-enhanced-chat-load-session)
    (define-key map (kbd "C-c C-a") 'ai-auto-complete-enhanced-chat-select-agent)
    (define-key map (kbd "C-c C-t") 'ai-auto-complete-enhanced-chat-toggle-timestamps)
    (define-key map (kbd "C-c C-r") 'ai-auto-complete-enhanced-chat-toggle-tool-results)
    (define-key map (kbd "C-c C-b") 'ai-auto-complete-enhanced-chat-toggle-sidebar)
    map)
  "Keymap for the enhanced chat interface.")

;; Define the enhanced chat mode
(define-minor-mode ai-auto-complete-enhanced-chat-mode
  "Minor mode for the enhanced chat interface."
  :init-value nil
  :lighter " EnhancedChat"
  ;; We manually manage the keymap's inclusion in minor-mode-overriding-map-alist
  ;; to ensure it takes precedence over the major mode's keymap for keys like RET.
  ;; Thus, the :keymap argument is not used here.
  (if ai-auto-complete-enhanced-chat-mode
      (progn ; Code to run when enabling the mode (e.g., keymap setup)
        (add-to-list 'minor-mode-overriding-map-alist (cons 'ai-auto-complete-enhanced-chat-mode ai-auto-complete-enhanced-chat-mode-map))
        ;; Initialization of buffer content is now handled by the main ai-auto-complete-enhanced-chat command
        (ai-auto-complete-enhanced-chat)
        )
    (progn ; Code to run when disabling the mode (e.g., keymap cleanup, marker cleanup)
      (setq minor-mode-overriding-map-alist (assq-delete-all 'ai-auto-complete-enhanced-chat-mode minor-mode-overriding-map-alist))
      (ai-auto-complete-enhanced-chat-cleanup))))

;; Main function to start the enhanced chat interface
(defun ai-auto-complete-enhanced-chat ()
  "Start or switch to the enhanced chat interface."
  (interactive)
  ;; Use its own dedicated buffer name
  (let ((chat-buffer (get-buffer-create ai-auto-complete-enhanced-chat-buffer-name)))
    (with-current-buffer chat-buffer
      ;; Set up the buffer with text-mode as the base mode
      (unless (eq major-mode 'text-mode)
        (text-mode))

      ;; Disable regular chat mode if it's enabled to avoid conflicts
      (when (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode)
        (ai-auto-complete-chat-mode -1))

      ;; Enable our enhanced chat minor mode
      (unless ai-auto-complete-enhanced-chat-mode
        (ai-auto-complete-enhanced-chat-mode 1))

      ;; Initialize/Re-initialize the chat buffer's content and structure.
      ;; This also sets buffer-read-only to nil and sets the input marker.
      (ai-auto-complete-enhanced-chat-initialize)
      )

    ;; Switch to the chat buffer
    (switch-to-buffer chat-buffer)
    ;; After switching, explicitly move point to the input marker.
    ;; ai-auto-complete-enhanced-chat-initialize sets this marker.
    (with-current-buffer chat-buffer
      (when (and ai-auto-complete-enhanced-chat-input-marker (marker-position ai-auto-complete-enhanced-chat-input-marker))
        (goto-char ai-auto-complete-enhanced-chat-input-marker)))))

;; Initialize the enhanced chat interface
(defun ai-auto-complete-enhanced-chat-initialize ()
  "Initialize the enhanced chat interface. This function erases the buffer
and sets up the header, content area, and input prompt."
  (let ((inhibit-read-only t)) ; Allow modifications even if buffer-read-only is t
    ;; Clear the buffer
    (erase-buffer)

    ;; Create the header section
    (insert (propertize "AI Auto Complete Enhanced Chat\n"
                       'face 'ai-auto-complete-enhanced-chat-header-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))
    (insert (propertize (format-time-string "Started on %Y-%m-%d %H:%M:%S\n")
                       'face 'ai-auto-complete-enhanced-chat-timestamp-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add control buttons
    (ai-auto-complete-enhanced-chat-insert-control-buttons)

    ;; Set the header marker
    (setq ai-auto-complete-enhanced-chat-header-marker (point-marker))

    ;; Add a separator
    (insert (propertize "\n" 'read-only t 'front-sticky t 'rear-nonsticky t))

    ;; Set the content marker
    (setq ai-auto-complete-enhanced-chat-content-marker (point-marker))

    ;; Add some instructions
    (insert (propertize "Type your message below and press Enter to send.\n\n"
                       'face 'ai-auto-complete-system-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add the input prompt - following the same pattern as chat.el
    (let ((prompt-start (point)))
      (insert (propertize "USER: "
                         'face 'ai-auto-complete-user-face
                         'read-only t
                         'front-sticky t
                         'rear-nonsticky t))
      ;; Set the input marker at the current position
      (setq ai-auto-complete-enhanced-chat-input-marker (point-marker))

      ;; Make sure the cursor is at the input position
      (goto-char (point-max))

      ;; Ensure the buffer is writable at the input position
      (put-text-property (point) (point) 'read-only nil))

    ;; Make sure the buffer is in a state where the user can type - CRITICAL!
    (setq buffer-read-only nil)))

;; Insert control buttons in the header
(defun ai-auto-complete-enhanced-chat-insert-control-buttons ()
  "Insert control buttons in the header of the enhanced chat interface."
  (let ((inhibit-read-only t))
    ;; Create a button bar
    (insert (propertize "[ "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add agent selection button
    (insert-text-button "Select Agent"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-select-agent-action
                       'follow-link t
                       'help-echo "Select an agent to chat with")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add sidebar toggle button
    (insert-text-button "Toggle Sidebar"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-toggle-sidebar-action
                       'follow-link t
                       'help-echo "Toggle the sidebar")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add save session button
    (insert-text-button "Save Session"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-save-session-action
                       'follow-link t
                       'help-echo "Save the current chat session")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add load session button
    (insert-text-button "Load Session"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-load-session-action
                       'follow-link t
                       'help-echo "Load a saved chat session")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add clear chat button
    (insert-text-button "Clear Chat"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-clear-action
                       'follow-link t
                       'help-echo "Clear the chat history")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add toggle timestamps button
    (insert-text-button (if ai-auto-complete-enhanced-chat-show-timestamps
                           "Hide Timestamps"
                         "Show Timestamps")
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-toggle-timestamps-action
                       'follow-link t
                       'help-echo "Toggle display of timestamps")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add toggle tool results button
    (insert-text-button (if ai-auto-complete-enhanced-chat-show-tool-results
                           "Hide Tool Results"
                         "Show Tool Results")
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-toggle-tool-results-action
                       'follow-link t
                       'help-echo "Toggle display of tool results")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add toggle markdown rendering button
    (insert-text-button (if (and (boundp 'ai-auto-complete-streaming-markdown-enabled)
                                ai-auto-complete-streaming-markdown-enabled)
                           "Streaming Mode"
                         "Markdown Mode")
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-toggle-markdown-action
                       'follow-link t
                       'help-echo "Toggle between streaming mode (real-time) and markdown mode (formatted from history)")

    (insert (propertize " ]\n"
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))))

;; Button action functions
(defun ai-auto-complete-enhanced-chat-select-agent-action (button)
  "Action for the select agent button."
  (ai-auto-complete-enhanced-chat-select-agent))

(defun ai-auto-complete-enhanced-chat-toggle-sidebar-action (button)
  "Action for the toggle sidebar button."
  (ai-auto-complete-enhanced-chat-toggle-sidebar))

(defun ai-auto-complete-enhanced-chat-save-session-action (button)
  "Action for the save session button."
  (ai-auto-complete-enhanced-chat-save-session))

(defun ai-auto-complete-enhanced-chat-load-session-action (button)
  "Action for the load session button."
  (ai-auto-complete-enhanced-chat-load-session))

(defun ai-auto-complete-enhanced-chat-clear-action (button)
  "Action for the clear chat button."
  (ai-auto-complete-enhanced-chat-clear))

(defun ai-auto-complete-enhanced-chat-toggle-timestamps-action (button)
  "Action for the toggle timestamps button."
  (ai-auto-complete-enhanced-chat-toggle-timestamps))

(defun ai-auto-complete-enhanced-chat-toggle-tool-results-action (button)
  "Action for the toggle tool results button."
  (ai-auto-complete-enhanced-chat-toggle-tool-results))

(defun ai-auto-complete-enhanced-chat-toggle-markdown-action (button)
  "Action for the toggle markdown rendering button."
  (when (fboundp 'ai-auto-complete-streaming-toggle-markdown)
    (ai-auto-complete-streaming-toggle-markdown)))

;; Main interactive functions
(defun ai-auto-complete-enhanced-chat-send-message ()
  "Send the current message in the enhanced chat interface."
  (interactive)
  (message "[DEBUG enhanced-chat-send-message] Called.")
  (when ai-auto-complete-enhanced-chat-mode
    (let ((input-text (buffer-substring-no-properties
                      ai-auto-complete-enhanced-chat-input-marker
                      (point-max))))
      (message "[DEBUG enhanced-chat-send-message] Input text: %s" input-text)
      (when (not (string-empty-p (string-trim input-text)))
        ;; Start streaming for the user message
      (ai-auto-complete-streaming-start 'user)
        (ai-auto-complete-streaming-update input-text)
        (ai-auto-complete-streaming-complete)

        ;; Check if the message is directed to a specific agent
        (if (and (string-match "^@\\([A-Za-z0-9_-]+\\)\\s-+\\(.+\\)$" input-text)
                 (boundp 'ai-auto-complete-agents-enabled)
                 ai-auto-complete-agents-enabled)
            (progn
              (message "[DEBUG enhanced-chat-send-message] Agent message detected.")
              (let ((agent-name (match-string 1 input-text))
                  (agent-message (match-string 2 input-text)))
              (message "[DEBUG enhanced-chat-send-message] Agent name: %s, Agent message: %s" agent-name agent-message)
              (setq ai-auto-complete-enhanced-chat-active-agent agent-name)

              ;; Start streaming for the agent response
              (ai-auto-complete-streaming-start 'agent agent-name)

              ;; Process the agent message (this would call the actual agent processing)
              (when (fboundp 'ai-auto-complete-process-agent-message)
               ; (message "DEBUG-Enhanced-CHAT-INITIAL: Adding response from agent %s to history" agent-name)
               ; (push (cons 'agent (cons agent-name agent-message)) ai-auto-complete--chat-history)
                (message "[DEBUG enhanced-chat-send-message] Calling ai-auto-complete-process-agent-message.")
                (ai-auto-complete-process-agent-message
                 agent-name
                 agent-message
                 ai-auto-complete--chat-history;; Pass chat history
                 (lambda (agent-name response)
                    ;; Add the response to history
                   ; (push (cons 'agent (cons agent-name response)) ai-auto-complete--chat-history)
                   (message "[DEBUG enhanced-chat-send-message] Agent response received: %s" response)
                   ;; Let the tools system handle the response processing and streaming
                   ;; The tools-default-callback will handle streaming via the integration advice
                  ; (ai-auto-complete-streaming-complete)
                   (ai-auto-complete-tools-default-callback response agent-name)
                   ))))
              )

          ;; Regular message to the default assistant
          (progn
            (message "[DEBUG enhanced-chat-send-message] Regular assistant message.")
            ;; Start streaming for the assistant response
            (ai-auto-complete-streaming-start 'assistant)

            ;; Process the message (this would call the actual backend)
            (let ((prompt (ai-auto-complete-chat-build-prompt)))
              (message "[DEBUG enhanced-chat-send-message] Built prompt: %s" prompt)
              (cond
               ;; Use native streaming if enabled
               ((and (boundp 'ai-auto-complete-native-streaming-enabled)
                     ai-auto-complete-native-streaming-enabled
                     (boundp 'ai-auto-complete-native-streaming-backends))
                (cond
                 ;; OpenAI with native streaming
                 ((and (boundp 'ai-auto-complete-backend) (eq ai-auto-complete-backend 'openai)
                       (memq 'openai ai-auto-complete-native-streaming-backends)
                       (fboundp 'ai-auto-complete-native-streaming-openai))
                  (message "[DEBUG enhanced-chat-send-message] Using native streaming for OpenAI.")
                  (ai-auto-complete-native-streaming-openai
                   prompt
                   (lambda (chunk) nil)  ; Streaming is handled internally
                   (lambda (error-msg) (message "Error: %s" error-msg))))

                 ;; Anthropic with native streaming
                 ((and (boundp 'ai-auto-complete-backend) (eq ai-auto-complete-backend 'anthropic)
                       (memq 'anthropic ai-auto-complete-native-streaming-backends)
                       (fboundp 'ai-auto-complete-native-streaming-anthropic))
                  (message "[DEBUG enhanced-chat-send-message] Using native streaming for Anthropic.")
                  (ai-auto-complete-native-streaming-anthropic
                   prompt
                   (lambda (chunk) nil)  ; Streaming is handled internally
                   (lambda (error-msg) (message "Error: %s" error-msg))))

                 ;; Fall back to simulated streaming for other backends
                 (t
                  (ai-auto-complete-enhanced-chat-send-message-fallback prompt))))
               ;; Use simulated streaming for all backends
               (t
                (message "[DEBUG enhanced-chat-send-message] Falling back to simulated streaming.")
                (ai-auto-complete-enhanced-chat-send-message-fallback prompt))))))

        ;; Clear the input area and ensure input marker is properly maintained
        ;; (let ((inhibit-read-only t))
        ;;   (message "[DEBUG enhanced-chat-send-message] Clearing input area.")
        ;;   (delete-region ai-auto-complete-enhanced-chat-input-marker (point-max))
        ;;   (goto-char ai-auto-complete-enhanced-chat-input-marker)
        ;;   ;; Ensure the input marker is still valid and the buffer is writable at that position
        ;;   (put-text-property (point) (point) 'read-only nil))
          )))
  (message "[DEBUG enhanced-chat-send-message] Exiting."))

(defun ai-auto-complete-enhanced-chat-send-message-fallback (prompt)
  "Send a message using simulated streaming as a fallback.
PROMPT is the prompt to send to the backend."
  (message "[DEBUG enhanced-chat-send-message-fallback] Called with prompt: %s" prompt)
  (cond
   ((eq ai-auto-complete-backend 'gemini)
    (when (fboundp 'ai-auto-complete--gemini-complete)
      (ai-auto-complete--gemini-complete prompt
                                       (lambda (response)
                                         (message "[DEBUG enhanced-chat-fallback] Gemini response received, simulating streaming...")
                                         (ai-auto-complete-streaming-simulate response 'assistant)))))
   ((eq ai-auto-complete-backend 'openai)
    (when (fboundp 'ai-auto-complete--openai-complete)
      (ai-auto-complete--openai-complete prompt
                                       (lambda (response)
                                         (message "[DEBUG enhanced-chat-fallback] OpenAI response received, simulating streaming...")
                                         (ai-auto-complete-streaming-simulate response 'assistant)))))
   ((eq ai-auto-complete-backend 'anthropic)
    (when (fboundp 'ai-auto-complete--anthropic-complete)
      (ai-auto-complete--anthropic-complete prompt
                                          (lambda (response)
                                            (message "[DEBUG enhanced-chat-fallback] Anthropic response received, simulating streaming...")
                                            (ai-auto-complete-streaming-simulate response 'assistant)))))
   ((eq ai-auto-complete-backend 'openrouter)
    (when (fboundp 'ai-auto-complete--openrouter-complete)
      (ai-auto-complete--openrouter-complete prompt
                                           (lambda (response)
                                             (message "[DEBUG enhanced-chat-fallback] OpenRouter response received, simulating streaming...")
                                             (ai-auto-complete-streaming-simulate response 'assistant)))))))

(defun ai-auto-complete-enhanced-chat-select-agent ()
  "Select an agent to chat with."
  (interactive)
  (when (and (boundp 'ai-auto-complete-agents-enabled)
             ai-auto-complete-agents-enabled
             (boundp 'ai-auto-complete-agents)
             (hash-table-p ai-auto-complete-agents))
    (let* ((agents (hash-table-keys ai-auto-complete-agents))
           (agent-name (completing-read "Select agent: " agents nil t)))
      (when (not (string-empty-p agent-name))
        (setq ai-auto-complete-enhanced-chat-active-agent agent-name)
        (message "Selected agent: %s" agent-name)))))

(defun ai-auto-complete-enhanced-chat-save-session ()
  "Save the current chat session."
  (interactive)
  (when (fboundp 'ai-auto-complete-chat-save-session)
    (ai-auto-complete-chat-save-session)))

(defun ai-auto-complete-enhanced-chat-load-session ()
  "Load a saved chat session."
  (interactive)
  (when (fboundp 'ai-auto-complete-chat-load-session)
    (ai-auto-complete-chat-load-session)))

(defun ai-auto-complete-enhanced-chat-clear ()
  "Clear the chat history."
  (interactive)
  (when (yes-or-no-p "Clear the chat history? ")
    (setq ai-auto-complete--chat-history nil)
    (ai-auto-complete-enhanced-chat-initialize)))

(defun ai-auto-complete-enhanced-chat-toggle-timestamps ()
  "Toggle display of timestamps."
  (interactive)
  (setq ai-auto-complete-enhanced-chat-show-timestamps
        (not ai-auto-complete-enhanced-chat-show-timestamps))
  (message "Timestamps %s"
           (if ai-auto-complete-enhanced-chat-show-timestamps "enabled" "disabled"))
  (ai-auto-complete-enhanced-chat-refresh))

(defun ai-auto-complete-enhanced-chat-toggle-tool-results ()
  "Toggle display of tool results."
  (interactive)
  (setq ai-auto-complete-enhanced-chat-show-tool-results
        (not ai-auto-complete-enhanced-chat-show-tool-results))
  (message "Tool results %s"
           (if ai-auto-complete-enhanced-chat-show-tool-results "enabled" "disabled"))
  (ai-auto-complete-enhanced-chat-refresh))

(defun ai-auto-complete-enhanced-chat-toggle-sidebar ()
  "Toggle the sidebar for agent management and context display."
  (interactive)
  (if (and (boundp 'ai-auto-complete-sidebar-window)
           ai-auto-complete-sidebar-window
           (window-live-p ai-auto-complete-sidebar-window))
      ;; Sidebar is visible, close it
      (progn
        (when (fboundp 'ai-auto-complete-sidebar-close)
          (ai-auto-complete-sidebar-close))
        (message "Sidebar closed"))
    ;; Sidebar is not visible, show it
    (when (fboundp 'ai-auto-complete-sidebar-show)
      (ai-auto-complete-sidebar-show)
      (message "Sidebar opened"))))

(defun ai-auto-complete-enhanced-chat-refresh ()
  "Refresh the enhanced chat interface.
This function only updates the control buttons and preserves all conversation content."
  (when ai-auto-complete-enhanced-chat-mode
    (let ((inhibit-read-only t))
      ;; Save the current input text
      (let ((input-text (when (and ai-auto-complete-enhanced-chat-input-marker
                                  (marker-position ai-auto-complete-enhanced-chat-input-marker))
                         (buffer-substring-no-properties
                          ai-auto-complete-enhanced-chat-input-marker
                          (point-max)))))

        ;; Only refresh the control buttons in the header, not the entire interface
        ;; This preserves all conversation content displayed by streaming functions
        (when (and ai-auto-complete-enhanced-chat-header-marker
                   (marker-position ai-auto-complete-enhanced-chat-header-marker))
          (save-excursion
            ;; Find the control buttons section and update it
            (goto-char (point-min))
            (when (search-forward "[ " ai-auto-complete-enhanced-chat-header-marker t)
              (let ((buttons-start (match-beginning 0)))
                (when (search-forward " ]\n" ai-auto-complete-enhanced-chat-header-marker t)
                  (let ((buttons-end (match-end 0)))
                    ;; Delete the old buttons
                    (delete-region buttons-start buttons-end)
                    ;; Insert updated buttons
                    (goto-char buttons-start)
                    (ai-auto-complete-enhanced-chat-insert-control-buttons)))))))

        ;; Restore the input text if any
        (when input-text
          (goto-char ai-auto-complete-enhanced-chat-input-marker)
          (insert input-text))

        ;; Ensure the buffer is writable after refresh - CRITICAL!
        (setq buffer-read-only nil)))))

(defun ai-auto-complete-enhanced-chat-display-conversation ()
  "Display the current conversation history in the enhanced chat buffer."
  (message "[DEBUG display-conversation] Called! This should NOT happen during streaming!")
  (message "[DEBUG display-conversation] Current buffer: %s" (current-buffer))
  (message "[DEBUG display-conversation] History has %d messages"
           (if (boundp 'ai-auto-complete--chat-history) (length ai-auto-complete--chat-history) 0))
  (when (and (boundp 'ai-auto-complete--chat-history)
             ai-auto-complete--chat-history)
    (let ((inhibit-read-only t))
      ;; Find where the content begins
      (goto-char ai-auto-complete-enhanced-chat-content-marker)

      ;; Clear the content area
      (delete-region ai-auto-complete-enhanced-chat-content-marker
                    (if (and ai-auto-complete-enhanced-chat-input-marker
                            (marker-position ai-auto-complete-enhanced-chat-input-marker))
                        ai-auto-complete-enhanced-chat-input-marker
                      (point-max)))

      ;; Display each message in the history (in reverse order since history is newest-first)
      (let ((index 0))
        (dolist (msg (reverse ai-auto-complete--chat-history))
          (let ((role (car msg))
                (content (cdr msg)))
            (cond
             ((eq role 'user)
              (when ai-auto-complete-enhanced-chat-show-timestamps
                (insert (propertize (format-time-string "[%H:%M:%S] ")
                                   'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t)))
              (insert (propertize "USER: "
                                 'face 'ai-auto-complete-user-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))
              (insert (propertize (ai-auto-complete-markdown-render content)
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t
                                 'ai-auto-complete-message-role role
                                 'ai-auto-complete-message-index index)))

             ((eq role 'assistant)
              (when ai-auto-complete-enhanced-chat-show-timestamps
                (insert (propertize (format-time-string "[%H:%M:%S] ")
                                   'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t)))
              (insert (propertize "AI: "
                                 'face 'ai-auto-complete-assistant-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))
              (insert (propertize (ai-auto-complete-markdown-render content)
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t
                                 'ai-auto-complete-message-role role
                                 'ai-auto-complete-message-index index)))

             ((eq role 'agent)
              (when ai-auto-complete-enhanced-chat-show-timestamps
                (insert (propertize (format-time-string "[%H:%M:%S] ")
                                   'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t)))
              (let ((agent-name (car content))
                    (agent-content (cdr content)))
                (insert (propertize (format "AGENT-%s: " agent-name)
                                   'face 'ai-auto-complete-enhanced-chat-agent-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t))
                (insert (propertize (ai-auto-complete-markdown-render agent-content)
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t
                                   'ai-auto-complete-message-role role
                                   'ai-auto-complete-message-index index))))

             ((eq role 'tool)
              (when (and ai-auto-complete-enhanced-chat-show-tool-calls
                        ai-auto-complete-enhanced-chat-show-timestamps)
                (insert (propertize (format-time-string "[%H:%M:%S] ")
                                   'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t)))
              (when ai-auto-complete-enhanced-chat-show-tool-calls
                (insert (propertize "TOOL: "
                                   'face 'ai-auto-complete-enhanced-chat-tool-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t))
                (insert (propertize content
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t
                                   'ai-auto-complete-message-role role
                                   'ai-auto-complete-message-index index))))

             ((eq role 'tool-result)
              (when (and ai-auto-complete-enhanced-chat-show-tool-results
                        ai-auto-complete-enhanced-chat-show-timestamps)
                (insert (propertize (format-time-string "[%H:%M:%S] ")
                                   'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t)))
              (when ai-auto-complete-enhanced-chat-show-tool-results
                (insert (propertize "RESULT: "
                                   'face 'ai-auto-complete-enhanced-chat-tool-result-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t))
                (insert (propertize (ai-auto-complete-markdown-render content)
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t
                                   'ai-auto-complete-message-role role
                                   'ai-auto-complete-message-index index)))))

            ;; Add message actions if enabled
            (when (and (fboundp 'ai-auto-complete-message-actions-insert-buttons)
                      (boundp 'ai-auto-complete-message-actions-enabled)
                      ai-auto-complete-message-actions-enabled
                      (memq role '(user assistant agent)))
              (let ((msg-content (if (eq role 'agent) (cdr content) content)))
                (ai-auto-complete-message-actions-insert-buttons role msg-content index)))

            ;; Add a newline after each message
            (insert (propertize "\n\n"
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))

            ;; Increment the index
            (setq index (1+ index)))))

      ;; Add the input prompt - following the same pattern as chat.el
      (let ((prompt-start (point)))
        (insert (propertize "USER: "
                           'face 'ai-auto-complete-user-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        ;; Set the input marker at the current position
        (setq ai-auto-complete-enhanced-chat-input-marker (point-marker))

        ;; Make sure the cursor is at the input position
        (goto-char (point-max))

        ;; Ensure the buffer is writable at the input position
        (put-text-property (point) (point) 'read-only nil))

      ;; Make sure the buffer is in a state where the user can type - CRITICAL!
      (setq buffer-read-only nil))))

;; Cleanup function
(defun ai-auto-complete-enhanced-chat-cleanup ()
  "Clean up the enhanced chat interface."
  (when ai-auto-complete-enhanced-chat-input-marker
    (set-marker ai-auto-complete-enhanced-chat-input-marker nil))
  (when ai-auto-complete-enhanced-chat-header-marker
    (set-marker ai-auto-complete-enhanced-chat-header-marker nil))
  (when ai-auto-complete-enhanced-chat-content-marker
    (set-marker ai-auto-complete-enhanced-chat-content-marker nil))
  (when ai-auto-complete-enhanced-chat-footer-marker
    (set-marker ai-auto-complete-enhanced-chat-footer-marker nil)))

(provide 'ui/enhanced-chat)
;;; enhanced-chat.el ends here
