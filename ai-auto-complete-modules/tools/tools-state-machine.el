;;; tools-state-machine.el --- State machine for tools in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a state machine implementation for tools in the AI Auto Complete package.
;; It replaces the deeply nested callback structure with a flatter, more maintainable approach.

;;; Code:

(require 'cl-lib)

;; Helper function to detect if native function calling is being used
(defun ai-auto-complete-tools-using-native-function-calling-p (agent-name backend)
  "Check if AGENT-NAME with BACKEND is using native function calling.
Returns t if native function calling is enabled and supported, nil otherwise."
  (and (boundp 'ai-auto-complete-tools-enabled)
       ai-auto-complete-tools-enabled
       agent-name
       (eq backend 'gemini)  ; Currently only Gemini supports native function calling
       (fboundp 'ai-auto-complete-get-agent-tools)
       (ai-auto-complete-get-agent-tools agent-name)))

;; Helper function to format tool results for native function calling
(defun ai-auto-complete-tools-format-structured-result (tool-name result error-occurred error-msg)
  "Format a tool result for native function calling.
Returns a structured result object suitable for native function calling."
  (if error-occurred
      `((name . ,tool-name)
        (response . ((error . t)
                    (message . ,error-msg))))
    `((name . ,tool-name)
      (response . ((result . ,result))))))

;; Define a state object to track tool processing
(cl-defstruct (ai-auto-complete-tool-state (:constructor ai-auto-complete-tool-state-create)
                                          (:copier nil))
  "State object for tool processing."
  (response nil :read-only nil :documentation "Current response being processed.")
  (tool-calls nil :read-only nil :documentation "List of tool calls to process.")
  (current-tool-index 0 :read-only nil :documentation "Index of current tool being processed.")
  (tool-results "" :read-only nil :documentation "Accumulated tool results.")
  (structured-results nil :read-only nil :documentation "List of structured tool results for native function calling.")
  (success-count 0 :read-only nil :documentation "Number of successful tool calls.")
  (error-count 0 :read-only nil :documentation "Number of failed tool calls.")
  (depth 0 :read-only nil :documentation "Current recursion depth.")
  (callback nil :read-only nil :documentation "Final callback to call with result.")
  (backend nil :read-only nil :documentation "Backend to use for LLM calls.")
  (original-response nil :read-only nil :documentation "Original response with tool calls.")
  (agent-name nil :read-only nil :documentation "Name of the agent making the request.")
  (history nil :read-only nil :documentation "Conversation history for context.")
  (use-native-function-calling nil :read-only nil :documentation "Whether to use native function calling format."))

;; Trampoline function to flatten recursion
(defun ai-auto-complete-tools-trampoline (initial-fn &rest initial-args)
  "Execute INITIAL-FN with INITIAL-ARGS, then any returned functions until done.
This flattens recursion into iteration, preventing excessive nesting."
  (let ((result (apply initial-fn initial-args)))
    (while (and result (listp result) (functionp (car result)))
      (setq result (apply (car result) (cdr result))))
    result))

;; Main entry point for tool processing
(defun ai-auto-complete-tools-process-with-state-machine (response callback &optional agent-name)
  "Process tool calls in RESPONSE using a state machine and call CALLBACK with result.
AGENT-NAME is the optional name of the agent making the request."
  (message "Processing response with state machine")
  (let ((effective-callback (if callback
                               callback
                             (lambda (resp)
                               (ai-auto-complete-tools-default-callback resp agent-name))))
        ;; Get the current conversation history
        (current-history (when (and (boundp 'ai-auto-complete--chat-history)
                                   ai-auto-complete--chat-history)
                          ai-auto-complete--chat-history)))
    (cond
     ;; Check if tools are disabled
     ((not ai-auto-complete-tools-enabled)
      (message "Tools not enabled, returning original response")
      (funcall effective-callback response))

     ;; Check if the response is an error message
     ((or (string-match-p "^Error:" response) (string-match-p "^ERROR:" response))
      (message "Detected error response, not processing for tools: %s"
               (substring response 0 (min 100 (length response))))
      (funcall effective-callback response))

     ;; Check if the response is empty or nil
     ((or (null response) (string-empty-p (string-trim response)))
      (message "Empty response received, not processing for tools")
      (funcall effective-callback (or response "")))

     ;; Process normal response with the state machine
     (t
      (ai-auto-complete-tools-trampoline
       #'ai-auto-complete-tools-start-processing
       response effective-callback agent-name current-history)))))

;; Start processing a response
(defun ai-auto-complete-tools-start-processing (response callback &optional agent-name history)
  "Start processing RESPONSE for tool calls and call CALLBACK with result.
AGENT-NAME is the optional name of the agent making the request.
HISTORY is the conversation history to maintain context."
  (message "Starting tool processing")
  (message "ai-auto-complete-tools-start-processing called with history: %s"
           (if history (format "%d messages" (length history)) "nil"))

  ;; Check if the response is an error message
  (if (or (string-match-p "^Error:" response) (string-match-p "^ERROR:" response))
      (progn
        (message "Detected error response, not processing for tools: %s"
                 (substring response 0 (min 100 (length response))))
        (funcall callback response))

    ;; Check if there are any tool calls in the response
    (if (not (string-match-p "<tool name=" response))
        ;; No tool calls, just return the response
        (progn
              ;; If this response was from an agent and was supposed to be processed for tools,
              ;; but no tools were found, it should still be added to history if it hasn't been.
              ;; However, the calling context (e.g., streaming) is usually responsible for adding
              ;; the agent's textual response to history. This path implies the response
              ;; was already passed to the callback by the provider, which should have handled history.
              ;; So, no direct history modification here seems correct for this branch.
          (message "No tool calls found, returning response")
          (funcall callback response))
      ;; Parse tool calls
      (let ((tool-calls (ai-auto-complete-tools-parse-response response)))
        (message "Found %d tool calls in response" (length tool-calls))
        (if (null tool-calls)
            ;; No tool calls found after parsing
            (progn
              (message "No tool calls found after parsing, returning response")
              (funcall callback response))
          ;; Create initial state and start processing
              (progn
                ;; Add the agent's message (containing the tool call) to the global history
                ;; This is the message that initiated the tool processing.
                (when (and agent-name response (boundp 'ai-auto-complete--chat-history))
                  (message "[TOOLS-DEBUG] Adding agent's tool-calling message to global history: %s" agent-name)
                  (push (cons 'agent (cons agent-name response)) ai-auto-complete--chat-history))

                (let* ((backend (if (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode)
                                   ai-auto-complete-backend
                                 (ai-auto-complete-get-current-backend)))
                       (use-native-fc (ai-auto-complete-tools-using-native-function-calling-p agent-name backend))
                       (state (progn
                                (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                                  (message "[TOOLS-DEBUG] Native function calling enabled: %s (agent: %s, backend: %s)"
                                           (if use-native-fc "YES" "NO") agent-name backend))
                                (ai-auto-complete-tool-state-create
                               :response response ; Current response being processed (agent's message with tool calls)
                               :tool-calls tool-calls
                               :callback callback
                               :backend backend
                               :original-response response ; The very first response from LLM that had tool calls
                               :agent-name agent-name
                               :history ai-auto-complete--chat-history ; Use current global history after update
                               :use-native-function-calling use-native-fc))))
                  (list #'ai-auto-complete-tools-process-next-tool state))))))))

;; Process the next tool in the queue
(defun ai-auto-complete-tools-process-next-tool (state)
  "Process the next tool in STATE and return the next function to call."
  (let ((tool-calls (ai-auto-complete-tool-state-tool-calls state))
        (current-index (ai-auto-complete-tool-state-current-tool-index state)))

    ;; Check if we've processed all tools
    (if (>= current-index (length tool-calls))
        ;; All tools processed, continue with LLM
        (list #'ai-auto-complete-tools-continue-with-llm state)

      ;; Process the current tool
      (let* ((tool-call (nth current-index tool-calls))
             (tool-name (car tool-call))
             (params (cdr tool-call))
             (tool (gethash tool-name ai-auto-complete-tools))
             (tool-fn (and tool (plist-get tool :function))))

        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[TOOLS-DEBUG] Executing tool: %s" tool-name))

        ;; Execute the tool and update state
        (let ((result nil)
              (error-occurred nil)
              (error-msg nil))

          ;; Try to execute the tool
          (condition-case err
              (when tool-fn
                (setq result (funcall tool-fn params))
                (setf (ai-auto-complete-tool-state-success-count state)
                      (1+ (ai-auto-complete-tool-state-success-count state))))
            (error
             (setq error-occurred t)
             (setq error-msg (error-message-string err))
             (setf (ai-auto-complete-tool-state-error-count state)
                   (1+ (ai-auto-complete-tool-state-error-count state)))))

          ;; Handle the result or error
          (let* ((use-native-fc (ai-auto-complete-tool-state-use-native-function-calling state))
                 (formatted-result
                  (cond
                   (error-occurred
                    (let ((msg (format "ERROR executing tool %s: %s" tool-name error-msg)))
                      (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                        (message "[TOOLS-DEBUG] %s" msg))
                      (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>" tool-name msg)))

                   ((not tool-fn)
                    (let ((msg (format "ERROR: Tool %s not found or has no function" tool-name)))
                      (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                        (message "[TOOLS-DEBUG] %s" msg))
                      (setf (ai-auto-complete-tool-state-error-count state)
                            (1+ (ai-auto-complete-tool-state-error-count state)))
                      (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>" tool-name msg)))

                   (t
                    (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                      (message "[TOOLS-DEBUG] Tool %s executed successfully" tool-name))
                    (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>" tool-name result)))))

            ;; For native function calling, also store structured results
            (when use-native-fc
              (let ((structured-result (ai-auto-complete-tools-format-structured-result
                                       tool-name result error-occurred
                                       (or error-msg (when (not tool-fn)
                                                      (format "Tool %s not found or has no function" tool-name))))))
                (setf (ai-auto-complete-tool-state-structured-results state)
                      (append (ai-auto-complete-tool-state-structured-results state)
                              (list structured-result)))))

            ;; Update state with the result
            (setf (ai-auto-complete-tool-state-tool-results state)
                  (concat (ai-auto-complete-tool-state-tool-results state) formatted-result))

            ;; Move to the next tool
            (setf (ai-auto-complete-tool-state-current-tool-index state) (1+ current-index))

            ;; Continue processing
            (list #'ai-auto-complete-tools-process-next-tool state)))))))

;; Continue the conversation with the LLM
(defun ai-auto-complete-tools-continue-with-llm (state)
  "Continue the conversation with the LLM using the results in STATE."
  (let ((tool-results (ai-auto-complete-tool-state-tool-results state))
        (success-count (ai-auto-complete-tool-state-success-count state))
        (error-count (ai-auto-complete-tool-state-error-count state))
        (tool-calls (ai-auto-complete-tool-state-tool-calls state))
        (original-response (ai-auto-complete-tool-state-original-response state))
        (callback (ai-auto-complete-tool-state-callback state))
        (backend (ai-auto-complete-tool-state-backend state))
        (depth (ai-auto-complete-tool-state-depth state))
        (agent-name (ai-auto-complete-tool-state-agent-name state)))

    ;; Log summary of tool execution
    (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
      (message "[TOOLS-DEBUG] Tool execution summary: %d successful, %d failed"
               success-count error-count))

    ;; Check if we've reached the maximum recursion depth
    (if (> depth 5)
        (progn
          (message "Maximum recursion depth reached, returning simplified response")
          ;; Ensure we preserve the agent-name when calling the callback
          (if (and callback (functionp callback))
              (funcall callback (concat original-response
                                      "\n\n[ERROR: Maximum tool recursion depth reached. Please continue without further tool calls.]"))
            ;; If no callback, use the default callback with agent-name
            (ai-auto-complete-tools-default-callback
             (concat original-response
                    "\n\n[ERROR: Maximum tool recursion depth reached. Please continue without further tool calls.]")
             agent-name)))

      ;; For native function calling, we don't need a continuation prompt
      ;; The tool results will be added to the conversation history and the LLM will continue naturally
      (let* ((response-without-tools (ai-auto-complete-tools-get-response-without-tools original-response)))


        ;; Add tool results to the GLOBAL conversation history (ai-auto-complete--chat-history)
        ;; This is the same history used by ai-auto-complete-process-agent-message
        (when (boundp 'ai-auto-complete--chat-history)
          ;; Check if we should use structured results for native function calling
          (let* ((use-native-fc (ai-auto-complete-tool-state-use-native-function-calling state))
                 (structured-results (ai-auto-complete-tool-state-structured-results state))
                 (tool-result-content (if (and use-native-fc structured-results)
                                        ;; Use structured format for native function calling
                                        (concat "STRUCTURED-TOOL-RESULTS: "
                                               (json-encode (vconcat structured-results)))
                                      ;; Use XML format for backward compatibility
                                      tool-results)))
            ;; Add the tool results to the history as a 'tool-result entry
            ;; This is the format expected by the backends (USER:, AGENT-<name>:, TOOL-RESULT:)
            (push (cons 'tool-result tool-result-content) ai-auto-complete--chat-history)

            ;; Debug logging for native function calling
            (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode use-native-fc)
              (message "[TOOLS-DEBUG] Using structured tool results for native function calling")
              (message "[TOOLS-DEBUG] Structured results count: %d" (length structured-results))
              (message "[TOOLS-DEBUG] Tool result content preview: %s"
                       (substring tool-result-content 0 (min 200 (length tool-result-content))))))
          (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
            (message "[TOOLS-DEBUG] Added tool results to GLOBAL conversation history")
            (message "[TOOLS-DEBUG] Global history now has %d messages" (length ai-auto-complete--chat-history))
            ;; Debug the history content
            (let ((count 0))
              (dolist (hist-msg ai-auto-complete--chat-history)
                (setq count (1+ count))
                (message "[TOOLS-DEBUG] History item %d - Type: %s, Role: %s"
                         count
                         (type-of hist-msg)
                         (if (listp hist-msg) (car hist-msg) "unknown"))))))

        ;; For native function calling, we need to continue the conversation
        ;; The tool results are now in the history, so we need to make another LLM call
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[TOOLS-DEBUG] Tool results added to history, making continuation call"))

        ;; Add more debug logging
        (message "[TOOLS-DEBUG] Continuation with agent-name: %s" (or agent-name "nil"))
        (message "[TOOLS-DEBUG] Callback type: %s" (type-of callback))

        ;; Make a continuation call to the LLM with the updated history
        (condition-case err
            (progn
              (message "[TOOLS-DEBUG] Making continuation call with updated history")

              ;; Use the GLOBAL conversation history (same as enhanced chat)
              ;; This ensures consistency with ai-auto-complete-process-agent-message
              (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                (message "[TOOLS-DEBUG] Global history has %d messages"
                         (if (boundp 'ai-auto-complete--chat-history)
                             (length ai-auto-complete--chat-history) 0)))

              ;; Call ai-auto-complete-process-agent-message to continue the conversation
              ;; This is the SAME function used by enhanced chat, ensuring consistency
              (if (fboundp 'ai-auto-complete-process-agent-message)
                  (progn
                    (message "[TOOLS-DEBUG] Calling ai-auto-complete-process-agent-message for continuation")
                    ;; Use an empty message to trigger continuation with the updated history
                    (ai-auto-complete-process-agent-message
                     agent-name
                    (format "@" agent-name ": " response-without-tools)  ;; Empty message - let the LLM continue based on history with tool results
                     ai-auto-complete--chat-history  ;; Use the SAME global history
                     (lambda (agent-name response)
                       (message "[TOOLS-DEBUG] Continuation response received from agent processing")
                       ;; Handle the continuation response
                       ;(ai-auto-complete-tools-handle-continuation
                        ;continuation-response response-without-tools callback (1+ depth) agent-name)
                        (funcall callback response)
                        )))
                ;; Fallback: call provider directly (should not happen in normal operation)
                (let* ((agent-model (ai-auto-complete-get-agent-default-model agent-name))
                       (effective-backend (plist-get agent-model :backend))
                       (provider-fn (gethash effective-backend ai-auto-complete--provider-functions))
                       (model-tag (plist-get agent-model :tag))
                       (model-name (ai-auto-complete-get-correct-model-name effective-backend model-tag))
                       (system-prompt (ai-auto-complete-get-system-prompt effective-backend agent-name)))

                  (when provider-fn
                    (message "[TOOLS-DEBUG] Fallback: calling provider function directly")
                    (funcall provider-fn
                             ai-auto-complete--chat-history  ;; Use global history
                             (lambda (continuation-response)
                               (message "[TOOLS-DEBUG] Continuation response received")
                               ;; Handle the continuation response
                               (ai-auto-complete-tools-handle-continuation
                                continuation-response response-without-tools callback (1+ depth) agent-name))
                             model-name
                             system-prompt
                             agent-name)))))

          ;; Handle any errors that occur during the LLM call
          (error
           (let ((error-msg (format "ERROR during LLM continuation call: %s"
                                    (error-message-string err))))
             (message "[TOOLS-ERROR] %s" error-msg)
             ;; Ensure we preserve the agent-name when calling the callback
             (if (and callback (functionp callback))
                 (funcall callback (concat response-without-tools
                                         "\n\n[ERROR: The AI encountered an error while processing tool results. "
                                         "Please try again or simplify your request.]"))
               ;; If no callback, use the default callback with agent-name
               (ai-auto-complete-tools-default-callback
                (concat response-without-tools
                       "\n\n[ERROR: The AI encountered an error while processing tool results. "
                       "Please try again or simplify your request.]")
                agent-name))))

        ;; Return nil to signal the end of this branch of the state machine
        nil)))))

;; Handle the continuation response from the LLM
(defun ai-auto-complete-tools-handle-continuation (continuation-response response-without-tools callback depth &optional agent-name)
  "Handle CONTINUATION-RESPONSE from the LLM.
RESPONSE-WITHOUT-TOOLS is the original response without tool calls.
CALLBACK is the function to call with the final result.
DEPTH is the current recursion depth.
AGENT-NAME is the optional name of the agent making the request."
  ;; Add debug logging
  (message "[TOOLS-DEBUG] ai-auto-complete-tools-handle-continuation called with agent-name: %s" (or agent-name "nil"))
  (message "[TOOLS-DEBUG] Callback type: %s" (type-of callback))

  ;; Use the GLOBAL conversation history (ai-auto-complete--chat-history)
  ;; This is the same history used throughout the system
  (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
    (message "[TOOLS-DEBUG] handle-continuation using GLOBAL history with %d messages"
             (if (boundp 'ai-auto-complete--chat-history)
                 (length ai-auto-complete--chat-history) 0))
    ;; Debug the history content
    (when (boundp 'ai-auto-complete--chat-history)
      (let ((count 0))
        (dolist (hist-msg ai-auto-complete--chat-history)
          (setq count (1+ count))
          (message "[TOOLS-DEBUG] History item %d - Type: %s, Role: %s"
                   count
                   (type-of hist-msg)
                   (if (listp hist-msg) (car hist-msg) "unknown"))))))
    ;; Check if the continuation response is an error
    (if (string-match-p "^Error:" continuation-response)
        (progn
          (message "Detected error in continuation response, not processing for tools: %s"
                   (substring continuation-response 0 (min 100 (length continuation-response))))
          ;; Ensure we preserve the agent-name when calling the callback
          (if (and callback (functionp callback))
              (funcall callback (concat response-without-tools
                                       "\n\n[ERROR: The AI encountered an error while processing tool results. "
                                       "Please try again or simplify your request.]"))
            ;; If no callback, use the default callback with agent-name
            (ai-auto-complete-tools-default-callback
             (concat response-without-tools
                    "\n\n[ERROR: The AI encountered an error while processing tool results. "
                    "Please try again or simplify your request.]")
             agent-name)))

    ;; Check if the continuation response has more tool calls
    (if (string-match-p "<tool name=" continuation-response)
        ;; If it does, process them with a new state machine
        (let ((new-tool-calls (ai-auto-complete-tools-parse-response continuation-response)))
          (if new-tool-calls
              ;; Pass the agent-name and updated history to the callback to ensure it's preserved
              (ai-auto-complete-tools-trampoline
               #'ai-auto-complete-tools-start-processing
               continuation-response
               (lambda (final-response)
                 ;; Ensure we preserve the agent-name when calling the callback
                 (if (and callback (functionp callback))
                     (funcall callback (concat response-without-tools "\n\n" final-response))
                   ;; If no callback, use the default callback with agent-name
                   (ai-auto-complete-tools-default-callback
                    (concat response-without-tools "\n\n" final-response)
                    agent-name)))
               agent-name
               ai-auto-complete--chat-history)  ;; Always pass the GLOBAL conversation history
            ;; No tool calls found (shouldn't happen given the string-match above)
            ;; Ensure we preserve the agent-name when calling the callback
            (if (and callback (functionp callback))
                (funcall callback (concat response-without-tools "\n\n" continuation-response))
              ;; If no callback, use the default callback with agent-name
              (ai-auto-complete-tools-default-callback
               (concat response-without-tools "\n\n" continuation-response)
               agent-name))))
      ;; No more tool calls, return the combined response
      ;; Ensure we preserve the agent-name when calling the callback
      (if (and callback (functionp callback))
          (funcall callback (concat response-without-tools "\n\n" continuation-response))
        ;; If no callback, use the default callback with agent-name
        (ai-auto-complete-tools-default-callback
         (concat response-without-tools "\n\n" continuation-response)
         agent-name)))))

(provide 'tools/tools-state-machine)
;;; tools-state-machine.el ends here
